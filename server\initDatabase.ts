import { db } from './db';
import { sql } from 'drizzle-orm';

export async function initializeDatabase() {
  try {
    if (!db) {
      console.log('Database connection not available, using fallback storage');
      return false;
    }

    console.log('Initializing database tables...');

    // Create users table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(255) PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        first_name <PERSON><PERSON><PERSON><PERSON>(255),
        last_name VA<PERSON><PERSON><PERSON>(255),
        profile_image_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);

    console.log('Users table created/verified');

    // Create user_settings table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS user_settings (
        id SERIAL PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        dark_mode BOOLEAN DEFAULT FALSE,
        auto_save_chats BOOLEAN DEFAULT TRUE,
        send_on_enter BOOLEAN DEFAULT TRUE,
        openai_api_key VARCHAR(255),
        gemini_api_key VARCHAR(255),
        cohere_api_key VARCHAR(255),
        anthropic_api_key VARCHAR(255),
        default_provider VARCHAR(50) DEFAULT 'openai',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id)
      )
    `);

    console.log('User settings table created/verified');

    // Create chats table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS chats (
        id SERIAL PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        type VARCHAR(50) DEFAULT 'text',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);

    console.log('Chats table created/verified');

    // Create messages table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS messages (
        id SERIAL PRIMARY KEY,
        chat_id INTEGER NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
        role VARCHAR(50) NOT NULL,
        content TEXT NOT NULL,
        image_url TEXT,
        metadata JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);

    console.log('Messages table created/verified');

    // Create indexes for better performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_chats_user_id ON chats(user_id)
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_chats_updated_at ON chats(updated_at DESC)
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id)
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at)
    `);

    console.log('Database indexes created/verified');
    console.log('Database initialization completed successfully');
    return true;
  } catch (error) {
    console.error('Database initialization failed:', error);
    console.log('Will use fallback storage instead');
    return false;
  }
}

import { supabase } from './supabaseStorage';

export async function initializeDatabase() {
  try {
    console.log('Initializing database tables...');

    // Create auth_users table
    const { error: authUsersError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS auth_users (
          id VARCHAR PRIMARY KEY,
          email VA<PERSON>HAR UNIQUE NOT NULL,
          first_name <PERSON><PERSON><PERSON><PERSON>,
          last_name <PERSON><PERSON><PERSON><PERSON>,
          profile_image_url VARCHAR,
          provider VARCHAR DEFAULT 'email',
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        );
      `
    });

    if (authUsersError) {
      console.log('Auth users table may already exist:', authUsersError.message);
    }

    // Create chats table
    const { error: chatsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS chats (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR NOT NULL REFERENCES auth_users(id) ON DELETE CASCADE,
          title VARCHAR NOT NULL,
          type VA<PERSON><PERSON><PERSON> DEFAULT 'text' CHECK (type IN ('text', 'image', 'code')),
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        );
      `
    });

    if (chatsError) {
      console.log('Chats table may already exist:', chatsError.message);
    }

    // Create messages table
    const { error: messagesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS messages (
          id SERIAL PRIMARY KEY,
          chat_id INTEGER NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
          role VARCHAR NOT NULL CHECK (role IN ('user', 'assistant')),
          content TEXT NOT NULL,
          image_url VARCHAR,
          metadata JSONB,
          created_at TIMESTAMP DEFAULT NOW()
        );
      `
    });

    if (messagesError) {
      console.log('Messages table may already exist:', messagesError.message);
    }

    // Create user_settings table
    const { error: settingsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS user_settings (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR NOT NULL REFERENCES auth_users(id) ON DELETE CASCADE,
          dark_mode BOOLEAN DEFAULT FALSE,
          auto_save_chats BOOLEAN DEFAULT TRUE,
          send_on_enter BOOLEAN DEFAULT TRUE,
          openai_api_key VARCHAR,
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW(),
          UNIQUE(user_id)
        );
      `
    });

    if (settingsError) {
      console.log('User settings table may already exist:', settingsError.message);
    }

    console.log('Database initialization completed');
    return true;
  } catch (error) {
    console.error('Database initialization failed:', error);
    
    // Fallback: Try to create tables using direct SQL execution
    try {
      console.log('Trying alternative table creation method...');
      
      // Since we can't use RPC, let's try to create a simple table structure
      // that works with the existing Supabase setup
      
      const tables = [
        {
          name: 'auth_users',
          sql: `
            id text primary key,
            email text unique not null,
            first_name text,
            last_name text,
            profile_image_url text,
            provider text default 'email',
            created_at timestamp with time zone default now(),
            updated_at timestamp with time zone default now()
          `
        },
        {
          name: 'chats',
          sql: `
            id bigserial primary key,
            user_id text not null references auth_users(id) on delete cascade,
            title text not null,
            type text default 'text' check (type in ('text', 'image', 'code')),
            created_at timestamp with time zone default now(),
            updated_at timestamp with time zone default now()
          `
        },
        {
          name: 'messages',
          sql: `
            id bigserial primary key,
            chat_id bigint not null references chats(id) on delete cascade,
            role text not null check (role in ('user', 'assistant')),
            content text not null,
            image_url text,
            metadata jsonb,
            created_at timestamp with time zone default now()
          `
        },
        {
          name: 'user_settings',
          sql: `
            id bigserial primary key,
            user_id text not null references auth_users(id) on delete cascade,
            dark_mode boolean default false,
            auto_save_chats boolean default true,
            send_on_enter boolean default true,
            openai_api_key text,
            created_at timestamp with time zone default now(),
            updated_at timestamp with time zone default now(),
            unique(user_id)
          `
        }
      ];

      for (const table of tables) {
        try {
          const { error } = await supabase
            .from(table.name)
            .select('id')
            .limit(1);
          
          if (error && error.code === '42P01') {
            console.log(`Table ${table.name} does not exist, but we cannot create it automatically.`);
            console.log(`Please create the table manually in Supabase with this structure:`);
            console.log(`CREATE TABLE ${table.name} (${table.sql});`);
          } else {
            console.log(`Table ${table.name} exists or is accessible`);
          }
        } catch (err) {
          console.log(`Could not check table ${table.name}:`, err);
        }
      }
      
      return false;
    } catch (fallbackError) {
      console.error('Fallback initialization also failed:', fallbackError);
      return false;
    }
  }
}

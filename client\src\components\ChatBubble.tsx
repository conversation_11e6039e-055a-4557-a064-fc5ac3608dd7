import { formatDistanceToNow } from "date-fns";
import { <PERSON><PERSON>, Share, RotateCcw, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { highlightCode } from "@/lib/codeHighlight";
import { useToast } from "@/hooks/use-toast";

interface ChatBubbleProps {
  type: "ai" | "user";
  content: string;
  timestamp: string;
  actions?: string[];
  isLoading?: boolean;
  imageUrl?: string;
  onRegenerate?: () => void;
}

export function ChatBubble({
  type,
  content,
  timestamp,
  actions = [],
  isLoading = false,
  imageUrl,
  onRegenerate,
}: ChatBubbleProps) {
  const { toast } = useToast();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      toast({
        title: "Copied!",
        description: "Message copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Unable to copy message to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          text: content,
          title: "AI Chat Message",
        });
      } catch (error) {
        console.log("Share cancelled");
      }
    } else {
      handleCopy();
    }
  };

  const handleDownload = () => {
    if (imageUrl) {
      const link = document.createElement("a");
      link.href = imageUrl;
      link.download = `ai-generated-image-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
        </div>
      );
    }

    // Check if content contains code blocks
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const hasCodeBlocks = codeBlockRegex.test(content);

    if (hasCodeBlocks) {
      const parts = content.split(codeBlockRegex);
      return (
        <div className="space-y-3">
          {parts.map((part, index) => {
            if (index % 3 === 0) {
              // Regular text
              return part ? (
                <p key={index} className="text-sm leading-relaxed whitespace-pre-wrap">
                  {part}
                </p>
              ) : null;
            } else if (index % 3 === 1) {
              // Language identifier
              return null;
            } else {
              // Code content
              const language = parts[index - 1] || "text";
              const highlightedCode = highlightCode(part, language);
              
              return (
                <div key={index} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border-l-4 border-green-500">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-gray-600 dark:text-gray-400 capitalize">
                      {language}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigator.clipboard.writeText(part)}
                      className="text-xs h-6 px-2"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      Copy
                    </Button>
                  </div>
                  <pre className="text-sm overflow-x-auto">
                    <code
                      className={`language-${language}`}
                      dangerouslySetInnerHTML={{ __html: highlightedCode }}
                    />
                  </pre>
                </div>
              );
            }
          })}
        </div>
      );
    }

    return (
      <div>
        {imageUrl && (
          <img
            src={imageUrl}
            alt="Generated content"
            className="w-full rounded-lg mb-3 max-w-sm"
          />
        )}
        <p className="text-sm leading-relaxed whitespace-pre-wrap">{content}</p>
      </div>
    );
  };

  return (
    <div className={`flex items-start space-x-3 animate-fadeIn ${type === "user" ? "justify-end" : ""}`}>
      {type === "ai" && (
        <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0">
          <span className="text-white text-sm">🤖</span>
        </div>
      )}

      <div className={`flex-1 ${type === "user" ? "flex justify-end" : ""}`}>
        <div
          className={`rounded-2xl px-4 py-3 max-w-sm ${
            type === "user"
              ? "bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-tr-md"
              : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 border border-gray-200 dark:border-gray-600 rounded-tl-md"
          }`}
        >
          {renderContent()}
        </div>

        {/* Message Actions */}
        {actions.length > 0 && !isLoading && (
          <div className="flex items-center space-x-3 mt-2 ml-1">
            {actions.includes("copy") && (
              <Button variant="ghost" size="sm" onClick={handleCopy} className="text-xs h-6 px-2">
                <Copy className="w-3 h-3 mr-1" />
                Copy
              </Button>
            )}
            {actions.includes("share") && (
              <Button variant="ghost" size="sm" onClick={handleShare} className="text-xs h-6 px-2">
                <Share className="w-3 h-3 mr-1" />
                Share
              </Button>
            )}
            {actions.includes("regenerate") && onRegenerate && (
              <Button variant="ghost" size="sm" onClick={onRegenerate} className="text-xs h-6 px-2">
                <RotateCcw className="w-3 h-3 mr-1" />
                Regenerate
              </Button>
            )}
            {actions.includes("download") && imageUrl && (
              <Button variant="ghost" size="sm" onClick={handleDownload} className="text-xs h-6 px-2">
                <Download className="w-3 h-3 mr-1" />
                Download
              </Button>
            )}
          </div>
        )}

        <p className="text-xs text-gray-400 dark:text-gray-500 mt-2 ml-1">
          {formatDistanceToNow(new Date(timestamp), { addSuffix: true })}
        </p>
      </div>

      {type === "user" && (
        <div className="w-8 h-8 bg-gray-400 rounded-lg flex items-center justify-center flex-shrink-0">
          <span className="text-white text-sm">👤</span>
        </div>
      )}
    </div>
  );
}

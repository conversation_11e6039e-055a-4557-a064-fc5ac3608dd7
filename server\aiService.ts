import OpenAI from 'openai';

export class AIService {
  private openai: OpenAI;

  constructor(apiKey?: string) {
    this.openai = new OpenAI({
      apiKey: apiKey || process.env.OPENAI_API_KEY,
    });
  }

  // Text generation for general chat
  async generateTextResponse(prompt: string, conversationHistory: Array<{role: 'user' | 'assistant', content: string}> = []): Promise<string> {
    try {
      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions. If the user asks about code, provide detailed explanations and examples when appropriate.'
        },
        ...conversationHistory.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        {
          role: 'user',
          content: prompt
        }
      ];

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',  // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages,
        max_tokens: 2000,
        temperature: 0.7,
      });

      return completion.choices[0]?.message?.content || 'I apologize, but I was unable to generate a response. Please try again.';
    } catch (error) {
      console.error('Error generating text response:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  // Code generation and analysis
  async generateCodeResponse(prompt: string, language?: string): Promise<string> {
    try {
      const systemPrompt = `You are an expert programmer and code assistant. You can:
1. Generate code in any programming language
2. Debug and fix code issues
3. Explain code functionality
4. Optimize code performance
5. Convert code between languages
6. Write unit tests

When providing code, always:
- Include clear comments
- Follow best practices
- Provide examples when helpful
- Explain the code functionality
- Format code properly with syntax highlighting markers

${language ? `Focus on ${language} programming language.` : ''}`;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4', // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 3000,
        temperature: 0.3,
      });

      return completion.choices[0]?.message?.content || 'I apologize, but I was unable to generate a code response. Please try again.';
    } catch (error) {
      console.error('Error generating code response:', error);
      throw new Error('Failed to generate code response');
    }
  }

  // Image generation using DALL-E
  async generateImage(prompt: string, size: '1024x1024' | '1024x1792' | '1792x1024' = '1024x1024'): Promise<string> {
    try {
      const response = await this.openai.images.generate({
        model: 'dall-e-3',
        prompt: prompt,
        n: 1,
        size: size,
        quality: 'standard',
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('No image data returned from OpenAI');
      }

      const imageUrl = response.data[0]?.url;
      if (!imageUrl) {
        throw new Error('No image URL returned from OpenAI');
      }

      return imageUrl;
    } catch (error) {
      console.error('Error generating image:', error);
      throw new Error('Failed to generate image');
    }
  }

  // Image analysis and description
  async analyzeImage(imageUrl: string, prompt?: string): Promise<string> {
    try {
      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: 'You are an expert at analyzing images. Provide detailed, accurate descriptions of what you see in images.'
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt || 'Please analyze this image and describe what you see in detail.'
            },
            {
              type: 'image_url',
              image_url: {
                url: imageUrl
              }
            }
          ]
        }
      ];

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4-vision-preview',
        messages,
        max_tokens: 1000,
      });

      return completion.choices[0]?.message?.content || 'I was unable to analyze this image. Please try again.';
    } catch (error) {
      console.error('Error analyzing image:', error);
      throw new Error('Failed to analyze image');
    }
  }

  // Code review and suggestions
  async reviewCode(code: string, language: string): Promise<string> {
    try {
      const prompt = `Please review this ${language} code and provide:
1. Code quality assessment
2. Security considerations
3. Performance improvements
4. Best practice suggestions
5. Bug identification
6. Refactoring recommendations

Code to review:
\`\`\`${language}
${code}
\`\`\``;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4', // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          {
            role: 'system',
            content: 'You are a senior software engineer specializing in code review. Provide thorough, constructive feedback on code quality, security, performance, and best practices.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2500,
        temperature: 0.2,
      });

      return completion.choices[0]?.message?.content || 'I was unable to review this code. Please try again.';
    } catch (error) {
      console.error('Error reviewing code:', error);
      throw new Error('Failed to review code');
    }
  }

  // Generate unit tests
  async generateUnitTests(code: string, language: string, framework?: string): Promise<string> {
    try {
      const frameworkText = framework ? ` using ${framework}` : '';
      const prompt = `Generate comprehensive unit tests for this ${language} code${frameworkText}:

\`\`\`${language}
${code}
\`\`\`

Please include:
1. Test cases for normal functionality
2. Edge cases and error handling
3. Mock objects where needed
4. Clear test descriptions
5. Setup and teardown if required`;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4', // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          {
            role: 'system',
            content: `You are an expert in writing comprehensive unit tests. Generate high-quality test cases that cover various scenarios including happy paths, edge cases, and error conditions.`
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 3000,
        temperature: 0.3,
      });

      return completion.choices[0]?.message?.content || 'I was unable to generate unit tests. Please try again.';
    } catch (error) {
      console.error('Error generating unit tests:', error);
      throw new Error('Failed to generate unit tests');
    }
  }

  // Explain code functionality
  async explainCode(code: string, language: string): Promise<string> {
    try {
      const prompt = `Please explain this ${language} code in detail:

\`\`\`${language}
${code}
\`\`\`

Include:
1. Overall purpose and functionality
2. Step-by-step breakdown
3. Key concepts and patterns used
4. Input/output explanation
5. Dependencies and requirements`;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4', // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
        messages: [
          {
            role: 'system',
            content: 'You are an expert programming teacher. Explain code clearly and thoroughly, making it accessible to developers of various skill levels.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.4,
      });

      return completion.choices[0]?.message?.content || 'I was unable to explain this code. Please try again.';
    } catch (error) {
      console.error('Error explaining code:', error);
      throw new Error('Failed to explain code');
    }
  }
}
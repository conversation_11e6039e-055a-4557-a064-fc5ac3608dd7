import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { AIService } from "./aiService";
import { MultiProviderAIService, type LLMProvider } from "./multiProviderAI";
import { authService } from "./authService";
import { z } from "zod";

// Middleware to check for user authentication
const requireAuth = (req: any, res: any, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ message: "Authentication required" });
  }

  const token = authHeader.split(' ')[1];
  const user = authService.verifyToken(token);

  if (!user) {
    return res.status(401).json({ message: "Invalid token" });
  }

  req.userId = user.id;
  req.user = user;
  next();
};

export async function registerRoutes(app: Express): Promise<Server> {

  // Health check
  app.get("/api/health", (req, res) => {
    res.json({ status: "ok", timestamp: new Date().toISOString() });
  });

  // Auth routes
  app.post('/api/auth/login', async (req, res) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ message: "Email and password required" });
      }

      const { user, token } = await authService.loginWithEmail(email, password);
      res.json({ user, token });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Login failed" });
    }
  });

  // Google OAuth login
  app.post('/api/auth/google', async (req, res) => {
    try {
      const { token: googleToken } = req.body;

      if (!googleToken) {
        return res.status(400).json({ message: "Google token required" });
      }

      const { user, token } = await authService.loginWithGoogle(googleToken);
      res.json({ user, token });
    } catch (error) {
      console.error("Google login error:", error);
      res.status(500).json({ message: "Google login failed" });
    }
  });

  app.get('/api/auth/user', requireAuth, async (req: any, res) => {
    try {
      const user = await authService.getUser(req.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Chat routes
  app.get("/api/chats", requireAuth, async (req: any, res) => {
    try {
      const chats = await storage.getChats(req.userId);
      res.json(chats);
    } catch (error) {
      console.error("Error fetching chats:", error);
      res.status(500).json({ message: "Failed to fetch chats" });
    }
  });

  app.get("/api/chats/:id", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }

      const chat = await storage.getChat(chatId, req.userId);
      if (!chat) {
        return res.status(404).json({ message: "Chat not found" });
      }

      res.json(chat);
    } catch (error) {
      console.error("Error fetching chat:", error);
      res.status(500).json({ message: "Failed to fetch chat" });
    }
  });

  app.post("/api/chats", requireAuth, async (req: any, res) => {
    try {
      const chatData = {
        userId: req.userId,
        title: req.body.title || 'New Chat',
        type: req.body.type || 'text'
      };

      const chat = await storage.createChat(chatData);
      res.json(chat);
    } catch (error) {
      console.error("Error creating chat:", error);
      res.status(500).json({ message: "Failed to create chat" });
    }
  });

  app.patch("/api/chats/:id", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }

      const updatedChat = await storage.updateChat(chatId, req.userId, req.body);
      if (!updatedChat) {
        return res.status(404).json({ message: "Chat not found" });
      }

      res.json(updatedChat);
    } catch (error) {
      console.error("Error updating chat:", error);
      res.status(500).json({ message: "Failed to update chat" });
    }
  });

  app.delete("/api/chats/:id", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }

      const deleted = await storage.deleteChat(chatId, req.userId);
      if (!deleted) {
        return res.status(404).json({ message: "Chat not found" });
      }

      res.json({ message: "Chat deleted successfully" });
    } catch (error) {
      console.error("Error deleting chat:", error);
      res.status(500).json({ message: "Failed to delete chat" });
    }
  });

  // Message routes
  app.get("/api/chats/:chatId/messages", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.chatId);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }

      const messages = await storage.getMessages(chatId);
      res.json(messages);
    } catch (error) {
      console.error("Error fetching messages:", error);
      res.status(500).json({ message: "Failed to fetch messages" });
    }
  });

  app.post("/api/chats/:chatId/messages", requireAuth, async (req: any, res) => {
    try {
      const chatId = parseInt(req.params.chatId);
      if (isNaN(chatId)) {
        return res.status(400).json({ message: "Invalid chat ID" });
      }

      // Verify chat ownership
      const chat = await storage.getChat(chatId, req.userId);
      if (!chat) {
        return res.status(404).json({ message: "Chat not found" });
      }

      const messageData = {
        chatId: chatId,
        role: req.body.role,
        content: req.body.content,
        imageUrl: req.body.image_url,
        metadata: req.body.metadata
      };

      const message = await storage.createMessage(messageData);
      res.json(message);
    } catch (error) {
      console.error("Error creating message:", error);
      res.status(500).json({ message: "Failed to create message" });
    }
  });

  // AI Generation routes
  app.post("/api/ai/chat", requireAuth, async (req: any, res) => {
    try {
      const { prompt, conversationHistory, provider } = req.body;

      if (!prompt || typeof prompt !== 'string') {
        return res.status(400).json({ message: "Prompt is required" });
      }

      // Get user settings to determine which provider to use
      const userSettings = await storage.getUserSettings(req.userId);
      const selectedProvider: LLMProvider = provider || userSettings?.defaultProvider || 'openai';

      // Get the appropriate API key for the selected provider
      let apiKey: string | undefined;
      let providerName: string;

      switch (selectedProvider) {
        case 'openai':
          apiKey = userSettings?.openaiApiKey;
          providerName = 'OpenAI';
          break;
        case 'gemini':
          apiKey = userSettings?.geminiApiKey;
          providerName = 'Google Gemini';
          break;
        case 'cohere':
          apiKey = userSettings?.cohereApiKey;
          providerName = 'Cohere';
          break;
        case 'anthropic':
          apiKey = userSettings?.anthropicApiKey;
          providerName = 'Anthropic Claude';
          break;
        default:
          return res.status(400).json({
            message: "Invalid provider specified",
            requiresApiKey: true
          });
      }

      if (!apiKey) {
        return res.status(400).json({
          message: `${providerName} API key required. Please add your API key in settings to use AI features.`,
          requiresApiKey: true,
          provider: selectedProvider
        });
      }

      const aiService = new MultiProviderAIService({
        provider: selectedProvider,
        apiKey: apiKey
      });

      const response = await aiService.generateTextResponse(prompt, conversationHistory || []);

      res.json({
        response,
        provider: selectedProvider,
        model: MultiProviderAIService.getProviderModel(selectedProvider)
      });
    } catch (error) {
      console.error("Error generating AI response:", error);
      if (error instanceof Error && error.message.includes('API key')) {
        return res.status(400).json({
          message: "Invalid API key. Please check your API key in settings.",
          requiresApiKey: true
        });
      }
      res.status(500).json({ message: "Failed to generate AI response" });
    }
  });

  app.post("/api/ai/code", requireAuth, async (req: any, res) => {
    try {
      const { prompt, language } = req.body;

      if (!prompt || typeof prompt !== 'string') {
        return res.status(400).json({ message: "Prompt is required" });
      }

      const userSettings = await storage.getUserSettings(req.userId);
      const apiKey = userSettings?.openaiApiKey;

      if (!apiKey) {
        return res.status(400).json({
          message: "OpenAI API key required. Please add your API key in settings to use AI features.",
          requiresApiKey: true
        });
      }

      const aiService = new AIService(apiKey);
      const response = await aiService.generateCodeResponse(prompt, language);

      res.json({ response });
    } catch (error) {
      console.error("Error generating code:", error);
      if (error instanceof Error && error.message.includes('API key')) {
        return res.status(400).json({
          message: "Invalid OpenAI API key. Please check your API key in settings.",
          requiresApiKey: true
        });
      }
      res.status(500).json({ message: "Failed to generate code" });
    }
  });

  app.post("/api/ai/image", requireAuth, async (req: any, res) => {
    try {
      const { prompt, size } = req.body;

      if (!prompt || typeof prompt !== 'string') {
        return res.status(400).json({ message: "Prompt is required" });
      }

      const userSettings = await storage.getUserSettings(req.userId);
      const apiKey = userSettings?.openaiApiKey;

      if (!apiKey) {
        return res.status(400).json({
          message: "OpenAI API key required. Please add your API key in settings to use AI features.",
          requiresApiKey: true
        });
      }

      const aiService = new AIService(apiKey);
      const imageUrl = await aiService.generateImage(prompt, size);

      res.json({ imageUrl });
    } catch (error) {
      console.error("Error generating image:", error);
      if (error instanceof Error && error.message.includes('API key')) {
        return res.status(400).json({
          message: "Invalid OpenAI API key. Please check your API key in settings.",
          requiresApiKey: true
        });
      }
      res.status(500).json({ message: "Failed to generate image" });
    }
  });

  // Settings routes
  app.get("/api/user/settings", requireAuth, async (req: any, res) => {
    try {
      const settings = await storage.getUserSettings(req.userId);
      res.json(settings);
    } catch (error) {
      console.error("Error fetching user settings:", error);
      res.status(500).json({ message: "Failed to fetch user settings" });
    }
  });

  app.post("/api/user/settings", requireAuth, async (req: any, res) => {
    try {
      const settingsData = {
        userId: req.userId,
        darkMode: req.body.dark_mode || false,
        autoSaveChats: req.body.auto_save_chats || true,
        sendOnEnter: req.body.send_on_enter || true,
        openaiApiKey: req.body.openai_api_key,
        geminiApiKey: req.body.gemini_api_key,
        cohereApiKey: req.body.cohere_api_key,
        anthropicApiKey: req.body.anthropic_api_key,
        defaultProvider: req.body.default_provider || 'openai'
      };

      const settings = await storage.upsertUserSettings(settingsData);
      res.json(settings);
    } catch (error) {
      console.error("Error updating user settings:", error);
      res.status(500).json({ message: "Failed to update user settings" });
    }
  });

  // Get available AI providers
  app.get("/api/ai/providers", requireAuth, async (req: any, res) => {
    try {
      const userSettings = await storage.getUserSettings(req.userId);

      const providers = [
        {
          id: 'openai',
          name: MultiProviderAIService.getProviderDisplayName('openai'),
          model: MultiProviderAIService.getProviderModel('openai'),
          hasApiKey: !!userSettings?.openaiApiKey,
          supportsImages: true,
          description: 'Advanced AI model with excellent reasoning and coding capabilities'
        },
        {
          id: 'gemini',
          name: MultiProviderAIService.getProviderDisplayName('gemini'),
          model: MultiProviderAIService.getProviderModel('gemini'),
          hasApiKey: !!userSettings?.geminiApiKey,
          supportsImages: false,
          description: 'Google\'s powerful AI model with strong performance across various tasks'
        },
        {
          id: 'cohere',
          name: MultiProviderAIService.getProviderDisplayName('cohere'),
          model: MultiProviderAIService.getProviderModel('cohere'),
          hasApiKey: !!userSettings?.cohereApiKey,
          supportsImages: false,
          description: 'Enterprise-focused AI model optimized for business applications'
        },
        {
          id: 'anthropic',
          name: MultiProviderAIService.getProviderDisplayName('anthropic'),
          model: MultiProviderAIService.getProviderModel('anthropic'),
          hasApiKey: !!userSettings?.anthropicApiKey,
          supportsImages: false,
          description: 'Constitutional AI model focused on being helpful, harmless, and honest'
        }
      ];

      res.json({
        providers,
        defaultProvider: userSettings?.defaultProvider || 'openai'
      });
    } catch (error) {
      console.error("Error fetching AI providers:", error);
      res.status(500).json({ message: "Failed to fetch AI providers" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
// Local storage fallback for when database is not available
interface LocalUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  profile_image_url?: string;
  provider?: 'email' | 'google';
  created_at: string;
  updated_at: string;
}

interface LocalChat {
  id: number;
  user_id: string;
  title: string;
  type: 'text' | 'image' | 'code';
  created_at: string;
  updated_at: string;
}

interface LocalMessage {
  id: number;
  chat_id: number;
  role: 'user' | 'assistant';
  content: string;
  image_url?: string;
  metadata?: any;
  created_at: string;
}

interface LocalUserSettings {
  id: number;
  user_id: string;
  dark_mode: boolean;
  auto_save_chats: boolean;
  send_on_enter: boolean;
  openai_api_key?: string;
  created_at: string;
  updated_at: string;
}

export class LocalStorage {
  private users: Map<string, LocalUser> = new Map();
  private chats: Map<number, LocalChat> = new Map();
  private messages: Map<number, LocalMessage[]> = new Map();
  private userSettings: Map<string, LocalUserSettings> = new Map();
  private nextChatId = 1;
  private nextMessageId = 1;
  private nextSettingsId = 1;

  // User operations
  async getUser(id: string): Promise<LocalUser | undefined> {
    return this.users.get(id);
  }

  async upsertUser(userData: Partial<LocalUser> & { id: string }): Promise<LocalUser> {
    const now = new Date().toISOString();
    const existingUser = this.users.get(userData.id);
    
    const user: LocalUser = {
      ...existingUser,
      ...userData,
      created_at: existingUser?.created_at || now,
      updated_at: now,
    };
    
    this.users.set(userData.id, user);
    return user;
  }

  // Chat operations
  async getChats(userId: string): Promise<LocalChat[]> {
    const userChats = Array.from(this.chats.values())
      .filter(chat => chat.user_id === userId)
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    
    return userChats;
  }

  async getChat(id: number, userId: string): Promise<LocalChat | undefined> {
    const chat = this.chats.get(id);
    if (chat && chat.user_id === userId) {
      return chat;
    }
    return undefined;
  }

  async createChat(chat: Omit<LocalChat, 'id' | 'created_at' | 'updated_at'>): Promise<LocalChat> {
    const now = new Date().toISOString();
    const newChat: LocalChat = {
      ...chat,
      id: this.nextChatId++,
      created_at: now,
      updated_at: now,
    };
    
    this.chats.set(newChat.id, newChat);
    this.messages.set(newChat.id, []); // Initialize empty messages array
    return newChat;
  }

  async updateChat(id: number, userId: string, updateData: Partial<LocalChat>): Promise<LocalChat | undefined> {
    const chat = this.chats.get(id);
    if (!chat || chat.user_id !== userId) {
      return undefined;
    }
    
    const updatedChat: LocalChat = {
      ...chat,
      ...updateData,
      updated_at: new Date().toISOString(),
    };
    
    this.chats.set(id, updatedChat);
    return updatedChat;
  }

  async deleteChat(id: number, userId: string): Promise<boolean> {
    const chat = this.chats.get(id);
    if (!chat || chat.user_id !== userId) {
      return false;
    }
    
    this.chats.delete(id);
    this.messages.delete(id);
    return true;
  }

  // Message operations
  async getMessages(chatId: number): Promise<LocalMessage[]> {
    return this.messages.get(chatId) || [];
  }

  async createMessage(message: Omit<LocalMessage, 'id' | 'created_at'>): Promise<LocalMessage> {
    const now = new Date().toISOString();
    const newMessage: LocalMessage = {
      ...message,
      id: this.nextMessageId++,
      created_at: now,
    };
    
    const chatMessages = this.messages.get(message.chat_id) || [];
    chatMessages.push(newMessage);
    this.messages.set(message.chat_id, chatMessages);
    
    return newMessage;
  }

  // User settings operations
  async getUserSettings(userId: string): Promise<LocalUserSettings | undefined> {
    return this.userSettings.get(userId);
  }

  async upsertUserSettings(settingsData: Partial<LocalUserSettings> & { user_id: string }): Promise<LocalUserSettings> {
    const now = new Date().toISOString();
    const existingSettings = this.userSettings.get(settingsData.user_id);
    
    const settings: LocalUserSettings = {
      id: existingSettings?.id || this.nextSettingsId++,
      user_id: settingsData.user_id,
      dark_mode: settingsData.dark_mode ?? false,
      auto_save_chats: settingsData.auto_save_chats ?? true,
      send_on_enter: settingsData.send_on_enter ?? true,
      openai_api_key: settingsData.openai_api_key,
      created_at: existingSettings?.created_at || now,
      updated_at: now,
    };
    
    this.userSettings.set(settingsData.user_id, settings);
    return settings;
  }
}

export const localStorage = new LocalStorage();

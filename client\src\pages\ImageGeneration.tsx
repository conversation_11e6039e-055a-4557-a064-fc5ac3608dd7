import { useState } from "react";
import { Link, useLocation } from "wouter";
import { useMutation } from "@tanstack/react-query";
import { ArrowLeft, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ImageCard } from "@/components/ImageCard";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface GeneratedImage {
  id: string;
  imageUrl: string;
  prompt: string;
  timestamp: string;
}

export default function ImageGeneration() {
  const [location] = useLocation();
  const params = new URLSearchParams(location.split("?")[1]);
  const chatId = params.get("chatId");
  
  const [prompt, setPrompt] = useState("");
  const [style, setStyle] = useState("realistic");
  const [size, setSize] = useState("1024x1024");
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const { toast } = useToast();

  const generateImageMutation = useMutation({
    mutationFn: async (data: { prompt: string; style: string; size: string }) => {
      const response = await apiRequest("POST", "/api/ai/image", data);
      return response.json();
    },
    onSuccess: (data) => {
      if (data.imageUrl) {
        const newImage: GeneratedImage = {
          id: Date.now().toString(),
          imageUrl: data.imageUrl,
          prompt: data.prompt,
          timestamp: new Date().toISOString(),
        };
        setGeneratedImages(prev => [newImage, ...prev]);
        setPrompt("");
        
        toast({
          title: "Image generated!",
          description: "Your image has been created successfully",
        });
      } else {
        toast({
          title: "Image generation not available",
          description: data.message || "Image generation is currently not supported",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Generation failed",
        description: "Unable to generate image. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleGenerate = () => {
    if (!prompt.trim()) {
      toast({
        title: "Prompt required",
        description: "Please enter a description for the image",
        variant: "destructive",
      });
      return;
    }

    generateImageMutation.mutate({
      prompt: prompt.trim(),
      style,
      size,
    });
  };

  const handleRegenerate = (originalPrompt: string) => {
    generateImageMutation.mutate({
      prompt: originalPrompt,
      style,
      size,
    });
  };

  const backUrl = chatId ? `/chat/${chatId}` : "/";

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-3">
            <Link href={backUrl}>
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </Button>
            </Link>
            <h1 className="text-lg font-semibold text-gray-800 dark:text-white">Generate Image</h1>
          </div>
        </div>
      </header>

      {/* Image Generation Content */}
      <div className="flex-1 px-4 py-6">
        {/* Prompt Input */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Describe the image you want to create
          </label>
          <Textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="A serene landscape with mountains and a lake at sunset..."
            className="min-h-[100px] bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 focus:ring-purple-500 focus:border-purple-500"
            disabled={generateImageMutation.isPending}
          />
        </div>

        {/* Generation Controls */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Style
            </label>
            <Select value={style} onValueChange={setStyle} disabled={generateImageMutation.isPending}>
              <SelectTrigger className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="realistic">Realistic</SelectItem>
                <SelectItem value="artistic">Artistic</SelectItem>
                <SelectItem value="cartoon">Cartoon</SelectItem>
                <SelectItem value="abstract">Abstract</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Size
            </label>
            <Select value={size} onValueChange={setSize} disabled={generateImageMutation.isPending}>
              <SelectTrigger className="bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1024x1024">1024x1024</SelectItem>
                <SelectItem value="1024x768">1024x768</SelectItem>
                <SelectItem value="768x1024">768x1024</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Generate Button */}
        <Button
          onClick={handleGenerate}
          disabled={!prompt.trim() || generateImageMutation.isPending}
          className="w-full bg-purple-500 hover:bg-purple-600 text-white font-semibold py-4 px-6 rounded-xl shadow-lg mb-6 transition-all duration-200 transform hover:scale-105 disabled:transform-none"
        >
          <Sparkles className="w-5 h-5 mr-2" />
          {generateImageMutation.isPending ? "Generating..." : "Generate Image"}
        </Button>

        {/* Information Card */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
          <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
            About Image Generation
          </h3>
          <p className="text-sm text-blue-700 dark:text-blue-300">
            This feature uses AI to generate images based on your text descriptions. 
            Currently, the Gemini API doesn't directly support image generation, so this is a demonstration of the interface.
            In a production environment, you would integrate with services like DALL-E, Midjourney, or Stable Diffusion.
          </p>
        </div>

        {/* Generated Images Gallery */}
        {generatedImages.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
              Generated Images
            </h2>
            {generatedImages.map((image) => (
              <ImageCard
                key={image.id}
                src={image.imageUrl}
                prompt={image.prompt}
                timestamp={image.timestamp}
                onRegenerate={() => handleRegenerate(image.prompt)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

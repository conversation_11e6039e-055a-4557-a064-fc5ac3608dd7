import jwt from 'jsonwebtoken';
import { OAuth2Client } from 'google-auth-library';

const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID;

const googleClient = new OAuth2Client(GOOGLE_CLIENT_ID);

export interface AuthUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  profile_image_url?: string;
  provider?: 'email' | 'google';
}

export class AuthService {
  // Generate JWT token
  generateToken(user: AuthUser): string {
    return jwt.sign(
      { 
        id: user.id, 
        email: user.email,
        provider: user.provider 
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );
  }

  // Verify JWT token
  verifyToken(token: string): AuthUser | null {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any;
      return decoded;
    } catch (error) {
      return null;
    }
  }

  // Email/Password Authentication
  async loginWithEmail(email: string, password: string): Promise<{ user: AuthUser; token: string }> {
    try {
      // For demo purposes, we'll accept any email/password combination
      // Use email as consistent user ID for demo
      const user: AuthUser = {
        id: email, // Use email as ID for consistency
        email,
        first_name: email.split('@')[0],
        provider: 'email' as const
      };

      const token = this.generateToken(user);
      return { user, token };
    } catch (error) {
      console.error('Email login error:', error);
      throw new Error('Login failed');
    }
  }

  // Google OAuth Authentication
  async loginWithGoogle(googleToken: string): Promise<{ user: AuthUser; token: string }> {
    try {
      // For demo purposes, we'll skip Google token verification
      // In production, you'd verify the token properly

      // For now, create a mock Google user
      const user: AuthUser = {
        id: `google_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        email: '<EMAIL>',
        first_name: 'Google',
        last_name: 'User',
        profile_image_url: 'https://via.placeholder.com/150',
        provider: 'google' as const
      };

      const token = this.generateToken(user);
      return { user, token };
    } catch (error) {
      console.error('Google login error:', error);
      throw new Error('Google login failed');
    }
  }

  // Get user by ID (simplified for demo)
  async getUser(id: string): Promise<AuthUser | null> {
    try {
      // For demo purposes, return user data based on ID
      // In production, you'd fetch from database
      return {
        id,
        email: id, // Since we use email as ID
        first_name: id.split('@')[0],
        provider: 'email'
      };
    } catch (error) {
      return null;
    }
  }
}

export const authService = new AuthService();

import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

neonConfig.webSocketConstructor = ws;

// Create a fallback database connection
let db: any = null;
let pool: Pool | null = null;

try {
  if (process.env.DATABASE_URL) {
    pool = new Pool({ connectionString: process.env.DATABASE_URL });
    db = drizzle({ client: pool, schema });
    console.log('Connected to Neon database');
  } else {
    console.log('DATABASE_URL not set, database operations will use fallback storage');
  }
} catch (error) {
  console.log('Failed to connect to Neon database, using fallback storage:', error);
}

export { pool, db };
// Simple code highlighting utility
// In a real application, you might want to use Prism.js or highlight.js

const keywords: Record<string, string[]> = {
  javascript: [
    'const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while',
    'class', 'import', 'export', 'default', 'async', 'await', 'try', 'catch',
    'throw', 'new', 'this', 'super', 'extends', 'static', 'typeof', 'instanceof'
  ],
  typescript: [
    'const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while',
    'class', 'import', 'export', 'default', 'async', 'await', 'try', 'catch',
    'throw', 'new', 'this', 'super', 'extends', 'static', 'typeof', 'instanceof',
    'interface', 'type', 'enum', 'namespace', 'declare', 'public', 'private',
    'protected', 'readonly', 'abstract'
  ],
  python: [
    'def', 'class', 'if', 'elif', 'else', 'for', 'while', 'try', 'except',
    'finally', 'with', 'import', 'from', 'as', 'return', 'yield', 'lambda',
    'and', 'or', 'not', 'in', 'is', 'None', 'True', 'False', 'async', 'await'
  ],
  java: [
    'public', 'private', 'protected', 'static', 'final', 'abstract', 'class',
    'interface', 'extends', 'implements', 'import', 'package', 'if', 'else',
    'for', 'while', 'do', 'switch', 'case', 'default', 'try', 'catch',
    'finally', 'throw', 'throws', 'new', 'this', 'super', 'return', 'void'
  ],
  html: [
    'html', 'head', 'body', 'title', 'meta', 'link', 'script', 'style',
    'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'a', 'img',
    'ul', 'ol', 'li', 'table', 'tr', 'td', 'th', 'form', 'input', 'button'
  ],
  css: [
    'color', 'background', 'font', 'margin', 'padding', 'border', 'width',
    'height', 'display', 'position', 'top', 'left', 'right', 'bottom',
    'float', 'clear', 'overflow', 'visibility', 'z-index', 'cursor'
  ]
};

const stringPatterns = [
  /"([^"\\]|\\.)*"/g,  // Double quotes
  /'([^'\\]|\\.)*'/g,  // Single quotes
  /`([^`\\]|\\.)*`/g   // Template literals
];

const commentPatterns = [
  /\/\/.*$/gm,         // Single line comments
  /\/\*[\s\S]*?\*\//g  // Multi-line comments
];

export function highlightCode(code: string, language: string): string {
  if (!code) return '';
  
  let highlighted = escapeHtml(code);
  const lang = language.toLowerCase();
  
  // Highlight comments first
  commentPatterns.forEach(pattern => {
    highlighted = highlighted.replace(pattern, (match) => 
      `<span class="text-gray-500 italic">${match}</span>`
    );
  });
  
  // Highlight strings
  stringPatterns.forEach(pattern => {
    highlighted = highlighted.replace(pattern, (match) => 
      `<span class="text-green-600">${match}</span>`
    );
  });
  
  // Highlight keywords
  if (keywords[lang]) {
    keywords[lang].forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlighted = highlighted.replace(regex, (match) => 
        `<span class="text-blue-600 font-semibold">${match}</span>`
      );
    });
  }
  
  // Highlight numbers
  highlighted = highlighted.replace(/\b\d+\.?\d*\b/g, (match) => 
    `<span class="text-orange-600">${match}</span>`
  );
  
  // Highlight functions (basic pattern)
  highlighted = highlighted.replace(/\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g, (match, funcName) => 
    `<span class="text-purple-600">${funcName}</span>(`
  );
  
  return highlighted;
}

function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

export function copyToClipboard(text: string): Promise<void> {
  return navigator.clipboard.writeText(text);
}

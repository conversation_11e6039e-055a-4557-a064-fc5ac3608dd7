import OpenAI from 'openai';

export interface ApiKeyValidationResult {
  isValid: boolean;
  provider: string;
  error?: string;
  model?: string;
}

export class ApiKeyValidator {
  
  static async validateOpenAI(apiKey: string): Promise<ApiKeyValidationResult> {
    try {
      const openai = new OpenAI({ apiKey });
      
      // Test with a simple completion request
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5,
      });
      
      return {
        isValid: true,
        provider: 'openai',
        model: 'GPT-3.5-Turbo'
      };
    } catch (error: any) {
      return {
        isValid: false,
        provider: 'openai',
        error: error.message || 'Invalid OpenAI API key'
      };
    }
  }

  static async validateGemini(apiKey: string): Promise<ApiKeyValidationResult> {
    try {
      const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${apiKey}`;
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            role: 'user',
            parts: [{ text: 'Hello' }]
          }],
          generationConfig: {
            maxOutputTokens: 5,
          }
        }),
      });

      if (response.ok) {
        return {
          isValid: true,
          provider: 'gemini',
          model: 'Gemini 1.5 Flash'
        };
      } else {
        const errorData = await response.json();
        return {
          isValid: false,
          provider: 'gemini',
          error: errorData.error?.message || 'Invalid Gemini API key'
        };
      }
    } catch (error: any) {
      return {
        isValid: false,
        provider: 'gemini',
        error: error.message || 'Invalid Gemini API key'
      };
    }
  }

  static async validateCohere(apiKey: string): Promise<ApiKeyValidationResult> {
    try {
      const response = await fetch('https://api.cohere.ai/v1/chat', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Hello',
          model: 'command-r-plus',
          max_tokens: 5,
        }),
      });

      if (response.ok) {
        return {
          isValid: true,
          provider: 'cohere',
          model: 'Command R+'
        };
      } else {
        const errorData = await response.json();
        return {
          isValid: false,
          provider: 'cohere',
          error: errorData.message || 'Invalid Cohere API key'
        };
      }
    } catch (error: any) {
      return {
        isValid: false,
        provider: 'cohere',
        error: error.message || 'Invalid Cohere API key'
      };
    }
  }

  static async validateAnthropic(apiKey: string): Promise<ApiKeyValidationResult> {
    try {
      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'x-api-key': apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: 'claude-3-5-sonnet-20241022',
          max_tokens: 5,
          messages: [{
            role: 'user',
            content: 'Hello'
          }],
        }),
      });

      if (response.ok) {
        return {
          isValid: true,
          provider: 'anthropic',
          model: 'Claude 3.5 Sonnet'
        };
      } else {
        const errorData = await response.json();
        return {
          isValid: false,
          provider: 'anthropic',
          error: errorData.error?.message || 'Invalid Anthropic API key'
        };
      }
    } catch (error: any) {
      return {
        isValid: false,
        provider: 'anthropic',
        error: error.message || 'Invalid Anthropic API key'
      };
    }
  }

  static async validateApiKey(provider: string, apiKey: string): Promise<ApiKeyValidationResult> {
    switch (provider) {
      case 'openai':
        return this.validateOpenAI(apiKey);
      case 'gemini':
        return this.validateGemini(apiKey);
      case 'cohere':
        return this.validateCohere(apiKey);
      case 'anthropic':
        return this.validateAnthropic(apiKey);
      default:
        return {
          isValid: false,
          provider,
          error: 'Unsupported provider'
        };
    }
  }
}

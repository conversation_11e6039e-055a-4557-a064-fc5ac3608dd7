import { Auth0Provider } from '@auth0/auth0-react';
import { ReactNode, createElement } from 'react';

export const auth0Config = {
  domain: 'dev-1idyeynotox4kdig.us.auth0.com',
  clientId: 'DZ6Zoh5Vs85B0XAzoTnGFFzab8UAhyi0',
  authorizationParams: {
    redirect_uri: typeof window !== 'undefined' ? window.location.origin : 'http://localhost:5000',
  },
};

interface Auth0ProviderWrapperProps {
  children: ReactNode;
}

export function Auth0ProviderWrapper({ children }: Auth0ProviderWrapperProps) {
  return createElement(Auth0Provider, {
    domain: auth0Config.domain,
    clientId: auth0Config.clientId,
    authorizationParams: auth0Config.authorizationParams,
  }, children);
}
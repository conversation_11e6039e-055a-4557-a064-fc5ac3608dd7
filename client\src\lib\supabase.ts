import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://qoqylojczbmncechnwnz.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvcXlsb2pjemJtbmNlY2hud256Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NDk1MTgsImV4cCI6MjA2NTEyNTUxOH0.S8IqYxyMLv6Fgg2vCz-Fx1jivU9Oa2g-S0xhWN-JioE'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Profile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  profile_image_url?: string
  created_at: string
  updated_at: string
}

export interface Chat {
  id: number
  user_id: string
  title: string
  type: 'text' | 'image' | 'code'
  created_at: string
  updated_at: string
}

export interface Message {
  id: number
  chat_id: number
  role: 'user' | 'assistant'
  content: string
  image_url?: string
  metadata?: any
  created_at: string
}

export interface UserSettings {
  id: number
  user_id: string
  dark_mode: boolean
  auto_save_chats: boolean
  send_on_enter: boolean
  openai_api_key?: string
  created_at: string
  updated_at: string
}
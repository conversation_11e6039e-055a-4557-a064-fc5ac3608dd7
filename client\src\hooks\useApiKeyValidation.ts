import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export interface ApiKeyValidationResult {
  isValid: boolean;
  provider: string;
  error?: string;
  model?: string;
}

export function useApiKeyValidation() {
  const { toast } = useToast();

  const validateApiKey = useMutation({
    mutationFn: async ({ provider, apiKey }: { provider: string; apiKey: string }): Promise<ApiKeyValidationResult> => {
      const response = await apiRequest("POST", "/api/validate-api-key", {
        provider,
        apiKey
      });
      return response.json();
    },
    onSuccess: (data) => {
      if (data.isValid) {
        toast({
          title: "API Key Valid",
          description: `${data.provider} API key is working correctly`,
        });
      } else {
        toast({
          title: "Invalid API Key",
          description: data.error || "Please check your API key",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Validation Failed",
        description: error.message || "Failed to validate API key",
        variant: "destructive",
      });
    },
  });

  return {
    validateApiKey: validateApiKey.mutate,
    isValidating: validateApiKey.isPending,
    validationResult: validateApiKey.data,
  };
}

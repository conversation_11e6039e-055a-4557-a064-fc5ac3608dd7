import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    let errorMessage = res.statusText;
    let requiresApiKey = false;
    let requiresAuth = false;
    let errorCode = 'UNKNOWN_ERROR';
    let provider = undefined;

    try {
      // Clone the response to avoid consuming the stream
      const responseClone = res.clone();
      const errorData = await responseClone.json();

      if (errorData.message) {
        errorMessage = errorData.message;
      }

      if (errorData.requiresApiKey) {
        requiresApiKey = true;
      }

      if (errorData.requiresAuth) {
        requiresAuth = true;
      }

      if (errorData.error) {
        errorCode = errorData.error;
      }

      if (errorData.provider) {
        provider = errorData.provider;
      }
    } catch (parseError) {
      // If JSON parsing fails, try to read as text
      try {
        const responseText = await res.text();
        if (responseText) {
          errorMessage = responseText;
        }
      } catch (textError) {
        // If both JSON and text parsing fail, use status text
        errorMessage = res.statusText || `HTTP ${res.status}`;
      }
    }

    const error = new Error(errorMessage);
    (error as any).status = res.status;
    (error as any).requiresApiKey = requiresApiKey;
    (error as any).requiresAuth = requiresAuth;
    (error as any).errorCode = errorCode;
    (error as any).provider = provider;

    // Handle authentication errors
    if (res.status === 401 || requiresAuth) {
      localStorage.removeItem('auth_token');
      window.location.reload();
    }

    throw error;
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const token = localStorage.getItem('auth_token');
  const headers: Record<string, string> = data ? { "Content-Type": "application/json" } : {};
  
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  const res = await fetch(url, {
    method,
    headers,
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const token = localStorage.getItem('auth_token');
    const headers: Record<string, string> = {};
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const res = await fetch(queryKey[0] as string, {
      headers,
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});

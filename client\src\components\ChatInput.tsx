import { useState, useRef, KeyboardEvent } from "react";
import { Send, Paperclip, Image } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface ChatInputProps {
  placeholder?: string;
  onSend: (message: string) => void;
  onImageGen?: () => void;
  onAttach?: () => void;
  isLoading?: boolean;
  maxLength?: number;
}

export function ChatInput({
  placeholder = "Type your message...",
  onSend,
  onImageGen,
  onAttach,
  isLoading = false,
  maxLength = 2000,
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    const trimmed = message.trim();
    if (trimmed && !isLoading) {
      onSend(trimmed);
      setMessage("");
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (value: string) => {
    setMessage(value);
    
    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-3">
      <div className="flex items-end space-x-3">
        {/* Attachment Button */}
        {onAttach && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onAttach}
            className="p-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <Paperclip className="w-5 h-5" />
          </Button>
        )}

        {/* Message Input */}
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="min-h-[44px] max-h-32 resize-none rounded-2xl pr-12 bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:ring-green-500 focus:border-green-500"
            disabled={isLoading}
          />
          
          {/* Character Count */}
          <div className="absolute bottom-1 right-1 text-xs text-gray-400 bg-white dark:bg-gray-700 px-1 rounded">
            {message.length}/{maxLength}
          </div>
        </div>

        {/* Image Generation Button */}
        {onImageGen && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onImageGen}
            className="p-3 text-purple-500 hover:text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20"
          >
            <Image className="w-5 h-5" />
          </Button>
        )}

        {/* Send Button */}
        <Button
          onClick={handleSend}
          disabled={!message.trim() || isLoading}
          className="p-3 bg-green-500 hover:bg-green-600 text-white rounded-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <Send className="w-5 h-5" />
        </Button>
      </div>
    </div>
  );
}

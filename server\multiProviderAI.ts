import OpenAI from 'openai';

export type LLMProvider = 'openai' | 'gemini' | 'cohere' | 'anthropic';

export interface LLMConfig {
  provider: LLMProvider;
  apiKey: string;
}

export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export class MultiProviderAIService {
  private openai?: OpenAI;

  constructor(private config: LLMConfig) {
    this.initializeProvider();
  }

  private initializeProvider() {
    switch (this.config.provider) {
      case 'openai':
        this.openai = new OpenAI({
          apiKey: this.config.apiKey,
        });
        break;
      case 'gemini':
        // Gemini initialization will be added
        break;
      case 'cohere':
        // Cohere initialization will be added
        break;
      case 'anthropic':
        // Anthropic initialization will be added
        break;
    }
  }

  async generateTextResponse(prompt: string, conversationHistory: ConversationMessage[] = []): Promise<string> {
    switch (this.config.provider) {
      case 'openai':
        return this.generateOpenAIResponse(prompt, conversationHistory);
      case 'gemini':
        return this.generateGeminiResponse(prompt, conversationHistory);
      case 'cohere':
        return this.generateCohereResponse(prompt, conversationHistory);
      case 'anthropic':
        return this.generateAnthropicResponse(prompt, conversationHistory);
      default:
        throw new Error(`Unsupported provider: ${this.config.provider}`);
    }
  }

  private async generateOpenAIResponse(prompt: string, conversationHistory: ConversationMessage[]): Promise<string> {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions.'
        },
        ...conversationHistory.map(msg => ({
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content
        })),
        {
          role: 'user',
          content: prompt
        }
      ];

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages,
        max_tokens: 2000,
        temperature: 0.7,
      });

      return completion.choices[0]?.message?.content || 'I apologize, but I was unable to generate a response. Please try again.';
    } catch (error) {
      console.error('Error generating OpenAI response:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  private async generateGeminiResponse(prompt: string, conversationHistory: ConversationMessage[]): Promise<string> {
    try {
      // Using Google's Generative AI REST API
      const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${this.config.apiKey}`;
      
      // Convert conversation history to Gemini format
      const contents = [];
      
      // Add conversation history
      for (const msg of conversationHistory) {
        contents.push({
          role: msg.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: msg.content }]
        });
      }
      
      // Add current prompt
      contents.push({
        role: 'user',
        parts: [{ text: prompt }]
      });

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents,
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 2000,
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Gemini API error: ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
      
      if (!generatedText) {
        throw new Error('No response generated from Gemini');
      }

      return generatedText;
    } catch (error) {
      console.error('Error generating Gemini response:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  private async generateCohereResponse(prompt: string, conversationHistory: ConversationMessage[]): Promise<string> {
    try {
      // Using Cohere's Chat API
      const url = 'https://api.cohere.ai/v1/chat';
      
      // Convert conversation history to Cohere format
      const chatHistory = conversationHistory.map(msg => ({
        role: msg.role === 'assistant' ? 'CHATBOT' : 'USER',
        message: msg.content
      }));

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: prompt,
          chat_history: chatHistory,
          model: 'command-r-plus',
          temperature: 0.7,
          max_tokens: 2000,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Cohere API error: ${errorData.message || response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.text) {
        throw new Error('No response generated from Cohere');
      }

      return data.text;
    } catch (error) {
      console.error('Error generating Cohere response:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  private async generateAnthropicResponse(prompt: string, conversationHistory: ConversationMessage[]): Promise<string> {
    try {
      // Using Anthropic's Messages API
      const url = 'https://api.anthropic.com/v1/messages';
      
      // Convert conversation history to Anthropic format
      const messages = [];
      
      for (const msg of conversationHistory) {
        if (msg.role !== 'system') {
          messages.push({
            role: msg.role,
            content: msg.content
          });
        }
      }
      
      // Add current prompt
      messages.push({
        role: 'user',
        content: prompt
      });

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'x-api-key': this.config.apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: 'claude-3-5-sonnet-20241022',
          max_tokens: 2000,
          temperature: 0.7,
          messages,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Anthropic API error: ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      const generatedText = data.content?.[0]?.text;
      
      if (!generatedText) {
        throw new Error('No response generated from Anthropic');
      }

      return generatedText;
    } catch (error) {
      console.error('Error generating Anthropic response:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  async generateImage(prompt: string, size: '1024x1024' | '1024x1792' | '1792x1024' = '1024x1024'): Promise<string> {
    // Only OpenAI supports image generation for now
    if (this.config.provider !== 'openai') {
      throw new Error('Image generation is only supported with OpenAI provider');
    }

    if (!this.openai) {
      throw new Error('OpenAI client not initialized');
    }

    try {
      const response = await this.openai.images.generate({
        model: 'dall-e-3',
        prompt: prompt,
        n: 1,
        size: size,
        quality: 'standard',
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('No image data returned from OpenAI');
      }

      const imageUrl = response.data[0]?.url;
      if (!imageUrl) {
        throw new Error('No image URL returned from OpenAI');
      }

      return imageUrl;
    } catch (error) {
      console.error('Error generating image:', error);
      throw new Error('Failed to generate image');
    }
  }

  static getProviderDisplayName(provider: LLMProvider): string {
    switch (provider) {
      case 'openai':
        return 'OpenAI GPT';
      case 'gemini':
        return 'Google Gemini';
      case 'cohere':
        return 'Cohere Command';
      case 'anthropic':
        return 'Anthropic Claude';
      default:
        return provider;
    }
  }

  static getProviderModel(provider: LLMProvider): string {
    switch (provider) {
      case 'openai':
        return 'GPT-4o';
      case 'gemini':
        return 'Gemini 1.5 Flash';
      case 'cohere':
        return 'Command R+';
      case 'anthropic':
        return 'Claude 3.5 Sonnet';
      default:
        return 'Unknown';
    }
  }
}

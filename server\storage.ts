import {
  users,
  chats,
  messages,
  userSettings,
  type User,
  type UpsertUser,
  type Chat,
  type InsertChat,
  type Message,
  type InsertMessage,
  type UserSettings,
  type InsertUserSettings,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and } from "drizzle-orm";
import { localStorage } from "./localStorage";

// Interface for storage operations
export interface IStorage {
  // User operations
  // (IMPORTANT) these user operations are mandatory for Replit Auth.
  getUser(id: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  
  // Chat operations
  getChats(userId: string): Promise<Chat[]>;
  getChat(id: number, userId: string): Promise<Chat | undefined>;
  createChat(chat: InsertChat): Promise<Chat>;
  updateChat(id: number, userId: string, data: Partial<InsertChat>): Promise<Chat | undefined>;
  deleteChat(id: number, userId: string): Promise<boolean>;
  
  // Message operations
  getMessages(chatId: number): Promise<Message[]>;
  createMessage(message: InsertMessage): Promise<Message>;
  
  // User settings operations
  getUserSettings(userId: string): Promise<UserSettings | undefined>;
  upsertUserSettings(settings: InsertUserSettings): Promise<UserSettings>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  // (IMPORTANT) these user operations are mandatory for Replit Auth.
  async getUser(id: string): Promise<User | undefined> {
    if (!db) {
      const localUser = await localStorage.getUser(id);
      if (!localUser) return undefined;

      // Convert local user format to schema format
      return {
        id: localUser.id,
        email: localUser.email,
        firstName: localUser.first_name || null,
        lastName: localUser.last_name || null,
        profileImageUrl: localUser.profile_image_url || null,
        createdAt: new Date(localUser.created_at),
        updatedAt: new Date(localUser.updated_at),
      };
    }

    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    if (!db) {
      const localUserData = {
        id: userData.id,
        email: userData.email || '',
        first_name: userData.firstName || undefined,
        last_name: userData.lastName || undefined,
        profile_image_url: userData.profileImageUrl || undefined,
      };

      const localUser = await localStorage.upsertUser(localUserData);

      // Convert back to schema format
      return {
        id: localUser.id,
        email: localUser.email,
        firstName: localUser.first_name || null,
        lastName: localUser.last_name || null,
        profileImageUrl: localUser.profile_image_url || null,
        createdAt: new Date(localUser.created_at),
        updatedAt: new Date(localUser.updated_at),
      };
    }

    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  // Chat operations
  async getChats(userId: string): Promise<Chat[]> {
    if (!db) {
      const localChats = await localStorage.getChats(userId);
      return localChats.map(chat => ({
        id: chat.id,
        userId: chat.user_id,
        title: chat.title,
        type: chat.type as 'text' | 'image' | 'code',
        createdAt: new Date(chat.created_at),
        updatedAt: new Date(chat.updated_at),
      }));
    }

    return await db
      .select()
      .from(chats)
      .where(eq(chats.userId, userId))
      .orderBy(desc(chats.updatedAt));
  }

  async getChat(id: number, userId: string): Promise<Chat | undefined> {
    if (!db) {
      const localChat = await localStorage.getChat(id, userId);
      if (!localChat) return undefined;

      return {
        id: localChat.id,
        userId: localChat.user_id,
        title: localChat.title,
        type: localChat.type as 'text' | 'image' | 'code',
        createdAt: new Date(localChat.created_at),
        updatedAt: new Date(localChat.updated_at),
      };
    }

    const [chat] = await db
      .select()
      .from(chats)
      .where(and(eq(chats.id, id), eq(chats.userId, userId)));
    return chat;
  }

  async createChat(chat: InsertChat): Promise<Chat> {
    if (!db) {
      const localChatData = {
        user_id: chat.userId,
        title: chat.title,
        type: chat.type || 'text' as 'text' | 'image' | 'code',
      };

      const localChat = await localStorage.createChat(localChatData);

      return {
        id: localChat.id,
        userId: localChat.user_id,
        title: localChat.title,
        type: localChat.type as 'text' | 'image' | 'code',
        createdAt: new Date(localChat.created_at),
        updatedAt: new Date(localChat.updated_at),
      };
    }

    const [newChat] = await db
      .insert(chats)
      .values(chat)
      .returning();
    return newChat;
  }

  async updateChat(id: number, userId: string, data: Partial<InsertChat>): Promise<Chat | undefined> {
    if (!db) {
      const localUpdateData = {
        title: data.title,
        type: data.type,
      };

      const localChat = await localStorage.updateChat(id, userId, localUpdateData);
      if (!localChat) return undefined;

      return {
        id: localChat.id,
        userId: localChat.user_id,
        title: localChat.title,
        type: localChat.type as 'text' | 'image' | 'code',
        createdAt: new Date(localChat.created_at),
        updatedAt: new Date(localChat.updated_at),
      };
    }

    const [updatedChat] = await db
      .update(chats)
      .set({ ...data, updatedAt: new Date() })
      .where(and(eq(chats.id, id), eq(chats.userId, userId)))
      .returning();
    return updatedChat;
  }

  async deleteChat(id: number, userId: string): Promise<boolean> {
    if (!db) {
      return await localStorage.deleteChat(id, userId);
    }

    const result = await db
      .delete(chats)
      .where(and(eq(chats.id, id), eq(chats.userId, userId)));
    return result.rowCount > 0;
  }

  // Message operations
  async getMessages(chatId: number): Promise<Message[]> {
    if (!db) {
      const localMessages = await localStorage.getMessages(chatId);
      return localMessages.map(msg => ({
        id: msg.id,
        chatId: msg.chat_id,
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
        imageUrl: msg.image_url,
        metadata: msg.metadata,
        createdAt: new Date(msg.created_at),
      }));
    }

    return await db
      .select()
      .from(messages)
      .where(eq(messages.chatId, chatId))
      .orderBy(messages.createdAt);
  }

  async createMessage(message: InsertMessage): Promise<Message> {
    if (!db) {
      const localMessageData = {
        chat_id: message.chatId,
        role: message.role,
        content: message.content,
        image_url: message.imageUrl,
        metadata: message.metadata,
      };

      const localMessage = await localStorage.createMessage(localMessageData);

      return {
        id: localMessage.id,
        chatId: localMessage.chat_id,
        role: localMessage.role as 'user' | 'assistant',
        content: localMessage.content,
        imageUrl: localMessage.image_url,
        metadata: localMessage.metadata,
        createdAt: new Date(localMessage.created_at),
      };
    }

    const [newMessage] = await db
      .insert(messages)
      .values(message)
      .returning();
    return newMessage;
  }

  // User settings operations
  async getUserSettings(userId: string): Promise<UserSettings | undefined> {
    if (!db) {
      const localSettings = await localStorage.getUserSettings(userId);
      if (!localSettings) return undefined;

      return {
        id: localSettings.id,
        userId: localSettings.user_id,
        darkMode: localSettings.dark_mode,
        autoSaveChats: localSettings.auto_save_chats,
        sendOnEnter: localSettings.send_on_enter,
        openaiApiKey: localSettings.openai_api_key,
        geminiApiKey: localSettings.gemini_api_key,
        cohereApiKey: localSettings.cohere_api_key,
        anthropicApiKey: localSettings.anthropic_api_key,
        defaultProvider: localSettings.default_provider as 'openai' | 'gemini' | 'cohere' | 'anthropic',
        createdAt: new Date(localSettings.created_at),
        updatedAt: new Date(localSettings.updated_at),
      };
    }

    const [settings] = await db
      .select()
      .from(userSettings)
      .where(eq(userSettings.userId, userId));
    return settings;
  }

  async upsertUserSettings(settingsData: InsertUserSettings): Promise<UserSettings> {
    if (!db) {
      const localSettingsData = {
        user_id: settingsData.userId,
        dark_mode: settingsData.darkMode || false,
        auto_save_chats: settingsData.autoSaveChats || true,
        send_on_enter: settingsData.sendOnEnter || true,
        openai_api_key: settingsData.openaiApiKey || undefined,
        gemini_api_key: settingsData.geminiApiKey || undefined,
        cohere_api_key: settingsData.cohereApiKey || undefined,
        anthropic_api_key: settingsData.anthropicApiKey || undefined,
        default_provider: (settingsData.defaultProvider || 'openai') as 'openai' | 'gemini' | 'cohere' | 'anthropic',
      };

      const localSettings = await localStorage.upsertUserSettings(localSettingsData);

      return {
        id: localSettings.id,
        userId: localSettings.user_id,
        darkMode: localSettings.dark_mode,
        autoSaveChats: localSettings.auto_save_chats,
        sendOnEnter: localSettings.send_on_enter,
        openaiApiKey: localSettings.openai_api_key,
        createdAt: new Date(localSettings.created_at),
        updatedAt: new Date(localSettings.updated_at),
      };
    }

    const [settings] = await db
      .insert(userSettings)
      .values(settingsData)
      .onConflictDoUpdate({
        target: userSettings.userId,
        set: {
          ...settingsData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return settings;
  }
}

export const storage = new DatabaseStorage();

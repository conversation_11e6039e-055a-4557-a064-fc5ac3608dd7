import { formatDistanceToNow } from "date-fns";
import { MessageCircle, Image, Code, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";

interface ChatListItemProps {
  id: number;
  title: string;
  lastMessage: string;
  timestamp: string;
  type: "text" | "image" | "code";
  isActive?: boolean;
  onSelect: (id: number) => void;
  onDelete?: (id: number) => void;
}

export function ChatListItem({
  id,
  title,
  lastMessage,
  timestamp,
  type,
  isActive = false,
  onSelect,
  onDelete,
}: ChatListItemProps) {
  const [showActions, setShowActions] = useState(false);

  const getIcon = () => {
    switch (type) {
      case "image":
        return <Image className="w-4 h-4 text-purple-500" />;
      case "code":
        return <Code className="w-4 h-4 text-green-500" />;
      default:
        return <MessageCircle className="w-4 h-4 text-blue-500" />;
    }
  };

  const getIconBg = () => {
    switch (type) {
      case "image":
        return "bg-purple-500";
      case "code":
        return "bg-green-500";
      default:
        return "bg-blue-500";
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(id);
    }
  };

  return (
    <div
      onClick={() => onSelect(id)}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      className={`relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl p-4 hover:shadow-md transition-all duration-200 cursor-pointer group ${
        isActive ? "ring-2 ring-green-500 border-green-500" : ""
      }`}
    >
      <div className="flex items-start space-x-3">
        <div className={`w-8 h-8 ${getIconBg()} rounded-lg flex items-center justify-center flex-shrink-0`}>
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-800 dark:text-white truncate">
            {title}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
            {lastMessage}
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
            {formatDistanceToNow(new Date(timestamp), { addSuffix: true })}
          </p>
        </div>

        {/* Delete Action */}
        {showActions && onDelete && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

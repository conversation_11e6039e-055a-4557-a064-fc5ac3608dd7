import { useEffect, useState } from "react";
import { <PERSON> } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Menu, Settings, User, MessageCircle, Image as ImageIcon, Code, Bot, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ChatListItem } from "@/components/ChatListItem";
import { useAuth0Integration } from "@/hooks/useAuth0";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Chat } from "@shared/schema";

export default function Dashboard() {
  const { user } = useAuth0Integration();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: chats = [], isLoading } = useQuery<Chat[]>({
    queryKey: ["/api/chats"],
  });

  const createChatMutation = useMutation({
    mutationFn: async (data: { title: string; type: "text" | "image" | "code" }) => {
      const response = await apiRequest("POST", "/api/chats", data);
      return response.json();
    },
    onSuccess: (newChat) => {
      queryClient.invalidateQueries({ queryKey: ["/api/chats"] });
      window.location.href = `/chat/${newChat.id}`;
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create new chat",
        variant: "destructive",
      });
    },
  });

  const deleteChatMutation = useMutation({
    mutationFn: async (chatId: number) => {
      await apiRequest("DELETE", `/api/chats/${chatId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/chats"] });
      toast({
        title: "Deleted",
        description: "Chat deleted successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete chat",
        variant: "destructive",
      });
    },
  });

  const handleNewChat = () => {
    createChatMutation.mutate({
      title: "New Chat",
      type: "text",
    });
  };

  const handleQuickAction = (type: "text" | "image" | "code") => {
    const titles = {
      text: "New Chat",
      image: "Image Generation",
      code: "Code Assistant",
    };
    
    createChatMutation.mutate({
      title: titles[type],
      type,
    });
  };

  const handleSelectChat = (chatId: number) => {
    window.location.href = `/chat/${chatId}`;
  };

  const handleDeleteChat = (chatId: number) => {
    deleteChatMutation.mutate(chatId);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading chats...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 flex">
      {/* Sidebar */}
      <div className="w-64 bg-gray-900 dark:bg-gray-800 flex flex-col">
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-700">
          <Button
            onClick={handleNewChat}
            disabled={createChatMutation.isPending}
            className="w-full bg-transparent border border-gray-600 hover:bg-gray-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200"
          >
            <Plus className="w-4 h-4 mr-2" />
            {createChatMutation.isPending ? "Creating..." : "New chat"}
          </Button>
        </div>

        {/* Chat History */}
        <div className="flex-1 overflow-y-auto p-2">
          {chats.length === 0 ? (
            <div className="text-center py-8 px-4">
              <MessageCircle className="w-8 h-8 text-gray-500 mx-auto mb-3" />
              <p className="text-gray-400 text-sm">No conversations yet</p>
            </div>
          ) : (
            <div className="space-y-1">
              {chats.map((chat) => (
                <ChatListItem
                  key={chat.id}
                  id={chat.id}
                  title={chat.title}
                  lastMessage="Click to continue conversation..."
                  timestamp={chat.updatedAt || chat.createdAt || new Date().toISOString()}
                  type={chat.type}
                  onSelect={handleSelectChat}
                  onDelete={handleDeleteChat}
                />
              ))}
            </div>
          )}
        </div>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-white text-sm font-medium truncate">
                {user?.email || 'User'}
              </p>
            </div>
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="p-1.5 text-gray-400 hover:text-white">
                <Settings className="w-4 h-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-white dark:bg-gray-900">
        {/* Main Chat Area */}
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="max-w-3xl mx-auto text-center">
            {/* ChatGPT Logo and Title */}
            <div className="mb-12">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-green-400 to-blue-500 rounded-full shadow-lg flex items-center justify-center">
                <Bot className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl font-semibold text-gray-800 dark:text-white mb-2">
                How can I help you today?
              </h1>
            </div>

            {/* Example Prompts */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="group cursor-pointer" onClick={() => handleNewChat('text')}>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-750 transition-colors duration-200">
                  <div className="flex items-start space-x-3">
                    <MessageCircle className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                    <div className="text-left">
                      <h3 className="font-medium text-gray-800 dark:text-white mb-1">
                        Start a conversation
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Ask me anything or start a discussion
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="group cursor-pointer" onClick={() => handleNewChat('image')}>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-750 transition-colors duration-200">
                  <div className="flex items-start space-x-3">
                    <ImageIcon className="w-5 h-5 text-purple-500 mt-0.5 flex-shrink-0" />
                    <div className="text-left">
                      <h3 className="font-medium text-gray-800 dark:text-white mb-1">
                        Generate images
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Create images from text descriptions
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="group cursor-pointer" onClick={() => handleNewChat('code')}>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-750 transition-colors duration-200">
                  <div className="flex items-start space-x-3">
                    <Code className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div className="text-left">
                      <h3 className="font-medium text-gray-800 dark:text-white mb-1">
                        Code assistance
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Write, debug, and explain code
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="group cursor-pointer" onClick={() => handleNewChat('text')}>
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-750 transition-colors duration-200">
                  <div className="flex items-start space-x-3">
                    <Bot className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                    <div className="text-left">
                      <h3 className="font-medium text-gray-800 dark:text-white mb-1">
                        Get creative
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Write stories, poems, or creative content
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer Note */}
            <div className="text-center mb-8">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                ChatGPT can make mistakes. Consider checking important information.
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Input Area */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="max-w-3xl mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder="Message ChatGPT..."
                className="w-full px-4 py-3 pr-12 bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                    handleNewChat('text');
                  }
                }}
              />
              <Button
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 p-2 rounded-lg"
                variant="ghost"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}



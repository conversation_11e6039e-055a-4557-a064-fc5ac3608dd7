import { formatDistanceToNow } from "date-fns";
import { Download, Share, RotateCcw, Maximize } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface ImageCardProps {
  src: string;
  prompt: string;
  timestamp: string;
  actions?: string[];
  onRegenerate?: () => void;
}

export function ImageCard({
  src,
  prompt,
  timestamp,
  actions = ["download", "share", "regenerate"],
  onRegenerate,
}: ImageCardProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleDownload = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(src);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `ai-generated-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast({
        title: "Downloaded!",
        description: "Image saved to your device",
      });
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Unable to download image",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        const response = await fetch(src);
        const blob = await response.blob();
        const file = new File([blob], "ai-generated-image.png", { type: "image/png" });
        
        await navigator.share({
          files: [file],
          title: "AI Generated Image",
          text: prompt,
        });
      } catch (error) {
        console.log("Share cancelled or failed");
      }
    } else {
      // Fallback: copy image URL
      try {
        await navigator.clipboard.writeText(src);
        toast({
          title: "Link copied!",
          description: "Image URL copied to clipboard",
        });
      } catch (error) {
        toast({
          title: "Share failed",
          description: "Unable to share image",
          variant: "destructive",
        });
      }
    }
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
        {/* Image */}
        <div className="relative group">
          <img
            src={src}
            alt={`Generated: ${prompt}`}
            className="w-full h-64 object-cover cursor-pointer"
            onClick={() => setIsFullscreen(true)}
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
            <Button
              variant="secondary"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              onClick={() => setIsFullscreen(true)}
            >
              <Maximize className="w-4 h-4 mr-2" />
              View Full
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
            "{prompt}"
          </p>

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {formatDistanceToNow(new Date(timestamp), { addSuffix: true })}
            </span>
            
            <div className="flex space-x-2">
              {actions.includes("download") && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDownload}
                  disabled={isLoading}
                  className="text-green-500 hover:text-green-600 text-xs h-8 px-2"
                >
                  <Download className="w-3 h-3 mr-1" />
                  Download
                </Button>
              )}
              
              {actions.includes("share") && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShare}
                  className="text-blue-500 hover:text-blue-600 text-xs h-8 px-2"
                >
                  <Share className="w-3 h-3 mr-1" />
                  Share
                </Button>
              )}
              
              {actions.includes("regenerate") && onRegenerate && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRegenerate}
                  className="text-purple-500 hover:text-purple-600 text-xs h-8 px-2"
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Regenerate
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
          onClick={() => setIsFullscreen(false)}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={src}
              alt={`Generated: ${prompt}`}
              className="max-w-full max-h-full object-contain"
            />
            <Button
              variant="secondary"
              size="sm"
              className="absolute top-4 right-4"
              onClick={() => setIsFullscreen(false)}
            >
              ✕
            </Button>
          </div>
        </div>
      )}
    </>
  );
}

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(142, 76%, 36%);
  --primary-foreground: hsl(355.7, 100%, 97.3%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* ChatGPT Clone specific colors */
  --chat-green: hsl(142, 76%, 36%);
  --chat-blue: hsl(243, 75%, 59%);
  --chat-purple: hsl(271, 76%, 53%);
  --chat-bg-light: hsl(40, 23%, 97%);
  --chat-surface-light: hsl(0, 0%, 100%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(142, 76%, 36%);
  --primary-foreground: hsl(355.7, 100%, 97.3%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  
  /* Dark mode chat colors */
  --chat-bg-dark: hsl(220, 13%, 18%);
  --chat-surface-dark: hsl(220, 13%, 25%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.2s ease-in;
  }

  .animate-slideUp {
    animation: slideUp 0.3s ease-out;
  }

  .animate-typing {
    animation: typing 1.5s infinite;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .chat-bubble-user {
    background: linear-gradient(135deg, var(--chat-green) 0%, var(--chat-blue) 100%);
  }

  .chat-bubble-ai {
    background: var(--chat-surface-light);
    border: 1px solid var(--border);
  }

  .dark .chat-bubble-ai {
    background: var(--chat-surface-dark);
    border-color: var(--border);
  }

  .gradient-bg {
    background: linear-gradient(135deg, var(--chat-blue) 0%, var(--chat-purple) 100%);
  }

  .code-block {
    background: hsl(220, 13%, 95%);
    border-left: 4px solid var(--chat-green);
  }

  .dark .code-block {
    background: hsl(220, 13%, 9%);
    border-left-color: var(--chat-green);
  }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { 
    transform: translateY(100%); 
    opacity: 0; 
  }
  100% { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes typing {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

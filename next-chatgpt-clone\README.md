# ChatGPT Clone Mobile App

A mobile-first ChatGPT clone built with Next.js App Router, Bootstrap UI, tRPC, Supabase, and Auth0.

## Features

- Text generation with Google Gemini API
- Image generation capabilities
- Mobile-optimized UI/UX
- Authentication via Auth0
- Chat history and management
- Dark mode support

## Tech Stack

- Next.js 14+ with App Router
- Bootstrap UI components
- tRPC + TanStack Query
- Auth0 for authentication
- Supabase for database
- Google Gemini API for AI capabilities

## Getting Started

```bash
# Install dependencies
npm install

# Run development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
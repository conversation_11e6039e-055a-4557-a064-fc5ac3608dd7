import { useState, useEffect } from "react";
import { <PERSON> } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ArrowLeft, User, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { useAuth0Integration } from "@/hooks/useAuth0";
import { useTheme } from "@/components/ThemeProvider";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { UserSettings } from "@shared/schema";

export default function Settings() {
  const { user, logout } = useAuth0Integration();
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [apiKey, setApiKey] = useState("");
  const [autoSaveChats, setAutoSaveChats] = useState(true);
  const [sendOnEnter, setSendOnEnter] = useState(true);

  const { data: settings } = useQuery<UserSettings>({
    queryKey: ["/api/user/settings"],
    retry: false,
  });

  const updateSettingsMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/user/settings", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user/settings"] });
      toast({
        title: "Settings saved",
        description: "Your preferences have been updated",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      });
    },
  });

  // Load settings when available
  useEffect(() => {
    if (settings) {
      setApiKey(settings.openaiApiKey || "");
      setAutoSaveChats(settings.autoSaveChats ?? true);
      setSendOnEnter(settings.sendOnEnter ?? true);
    }
  }, [settings]);

  const handleSaveApiKey = () => {
    updateSettingsMutation.mutate({
      openai_api_key: apiKey,
    });
  };

  const handleToggleAutoSave = (checked: boolean) => {
    setAutoSaveChats(checked);
    updateSettingsMutation.mutate({
      auto_save_chats: checked,
    });
  };

  const handleToggleSendOnEnter = (checked: boolean) => {
    setSendOnEnter(checked);
    updateSettingsMutation.mutate({
      send_on_enter: checked,
    });
  };

  const handleToggleDarkMode = (checked: boolean) => {
    const newTheme = checked ? "dark" : "light";
    setTheme(newTheme);
    updateSettingsMutation.mutate({
      dark_mode: checked,
    });
  };

  const handleLogout = () => {
    logout();
  };

  const handleExportChats = async () => {
    try {
      const response = await fetch("/api/chats", {
        credentials: "include",
      });
      if (!response.ok) throw new Error("Failed to fetch chats");
      
      const chats = await response.json();
      const dataStr = JSON.stringify(chats, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `chatgpt-clone-export-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Export successful",
        description: "Your chat history has been downloaded",
      });
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Unable to export chat history",
        variant: "destructive",
      });
    }
  };

  const isDarkMode = theme === "dark";

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </Button>
            </Link>
            <h1 className="text-lg font-semibold text-gray-800 dark:text-white">Settings</h1>
          </div>
        </div>
      </header>

      {/* Settings Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Profile Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Profile</h2>
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
              {user?.profileImageUrl ? (
                <img
                  src={user.profileImageUrl}
                  alt="Profile"
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="w-8 h-8 text-white" />
              )}
            </div>
            <div>
              <h3 className="font-medium text-gray-800 dark:text-white">
                {user?.firstName && user?.lastName
                  ? `${user.firstName} ${user.lastName}`
                  : user?.email || "User"}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {user?.email || "No email available"}
              </p>
            </div>
          </div>
        </div>

        {/* Appearance */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Appearance</h2>
          <div className="flex items-center justify-between">
            <span className="text-gray-700 dark:text-gray-300">Dark Mode</span>
            <Switch
              checked={isDarkMode}
              onCheckedChange={handleToggleDarkMode}
            />
          </div>
        </div>

        {/* Chat Preferences */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Chat Preferences</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">Auto-save chats</span>
              <Switch
                checked={autoSaveChats}
                onCheckedChange={handleToggleAutoSave}
              />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">Send on Enter</span>
              <Switch
                checked={sendOnEnter}
                onCheckedChange={handleToggleSendOnEnter}
              />
            </div>
          </div>
        </div>

        {/* API Configuration */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">API Settings</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                OpenAI API Key
              </label>
              <div className="flex space-x-2">
                <Input
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="Enter your OpenAI API key"
                  className="flex-1 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                />
                <Button
                  onClick={handleSaveApiKey}
                  disabled={updateSettingsMutation.isPending}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  {updateSettingsMutation.isPending ? "Saving..." : "Save"}
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Get your API key from{" "}
                <a
                  href="https://platform.openai.com/api-keys"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  OpenAI Platform
                </a>
              </p>
            </div>
          </div>
        </div>

        {/* Account Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Account</h2>
          <div className="space-y-3">
            <Button
              variant="outline"
              onClick={handleExportChats}
              className="w-full justify-start text-gray-700 dark:text-gray-300"
            >
              Export Chat History
            </Button>
          </div>
        </div>

        {/* Logout */}
        <Button
          onClick={handleLogout}
          className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200"
        >
          <LogOut className="w-5 h-5 mr-2" />
          Logout
        </Button>
      </div>
    </div>
  );
}

import { useState, useEffect } from "react";
import { Link } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ArrowLeft, User, LogOut, CheckCircle, XCircle, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { useAuth0Integration } from "@/hooks/useAuth0";
import { useApiKeyValidation } from "@/hooks/useApiKeyValidation";
import { useTheme } from "@/components/ThemeProvider";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { UserSettings } from "@shared/schema";

export default function Settings() {
  const { user, logout } = useAuth0Integration();
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { validateApiKey, isValidating } = useApiKeyValidation();

  const [openaiApiKey, setOpenaiApiKey] = useState("");
  const [geminiApiKey, setGeminiApiKey] = useState("");
  const [cohereApiKey, setCohereApiKey] = useState("");
  const [anthropicApiKey, setAnthropicApiKey] = useState("");
  const [defaultProvider, setDefaultProvider] = useState<'openai' | 'gemini' | 'cohere' | 'anthropic'>('openai');
  const [autoSaveChats, setAutoSaveChats] = useState(true);
  const [sendOnEnter, setSendOnEnter] = useState(true);

  // Track validation states
  const [validationStates, setValidationStates] = useState<Record<string, 'idle' | 'validating' | 'valid' | 'invalid'>>({
    openai: 'idle',
    gemini: 'idle',
    cohere: 'idle',
    anthropic: 'idle'
  });

  const { data: settings } = useQuery<UserSettings>({
    queryKey: ["/api/user/settings"],
    retry: false,
  });

  const updateSettingsMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/user/settings", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user/settings"] });
      toast({
        title: "Settings saved",
        description: "Your preferences have been updated",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      });
    },
  });

  // Load settings when available
  useEffect(() => {
    if (settings) {
      setOpenaiApiKey(settings.openaiApiKey || "");
      setGeminiApiKey(settings.geminiApiKey || "");
      setCohereApiKey(settings.cohereApiKey || "");
      ******************************************* || "");
      setDefaultProvider(settings.defaultProvider || 'openai');
      setAutoSaveChats(settings.autoSaveChats ?? true);
      setSendOnEnter(settings.sendOnEnter ?? true);
    }
  }, [settings]);

  const handleSaveApiKey = async (provider: string, apiKey: string) => {
    if (!apiKey.trim()) {
      toast({
        title: "Error",
        description: "API key cannot be empty",
        variant: "destructive",
      });
      return;
    }

    // Set validating state
    setValidationStates(prev => ({ ...prev, [provider]: 'validating' }));

    try {
      // Validate the API key first
      const response = await fetch('/api/validate-api-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({ provider, apiKey })
      });

      const validation = await response.json();

      if (validation.isValid) {
        // Save the API key if validation passes
        const updateData: any = {};
        updateData[`${provider}_api_key`] = apiKey;
        await updateSettingsMutation.mutateAsync(updateData);

        setValidationStates(prev => ({ ...prev, [provider]: 'valid' }));
        toast({
          title: "Success",
          description: `${provider} API key saved and validated successfully`,
        });
      } else {
        setValidationStates(prev => ({ ...prev, [provider]: 'invalid' }));
        toast({
          title: "Invalid API Key",
          description: validation.error || "Please check your API key",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      setValidationStates(prev => ({ ...prev, [provider]: 'invalid' }));
      toast({
        title: "Validation Failed",
        description: error.message || "Failed to validate API key",
        variant: "destructive",
      });
    }
  };

  const handleSaveDefaultProvider = (provider: 'openai' | 'gemini' | 'cohere' | 'anthropic') => {
    setDefaultProvider(provider);
    updateSettingsMutation.mutate({
      default_provider: provider,
    });
  };

  const getValidationIcon = (provider: string) => {
    const state = validationStates[provider];
    switch (state) {
      case 'validating':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'valid':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'invalid':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const handleToggleAutoSave = (checked: boolean) => {
    setAutoSaveChats(checked);
    updateSettingsMutation.mutate({
      auto_save_chats: checked,
    });
  };

  const handleToggleSendOnEnter = (checked: boolean) => {
    setSendOnEnter(checked);
    updateSettingsMutation.mutate({
      send_on_enter: checked,
    });
  };

  const handleToggleDarkMode = (checked: boolean) => {
    const newTheme = checked ? "dark" : "light";
    setTheme(newTheme);
    updateSettingsMutation.mutate({
      dark_mode: checked,
    });
  };

  const handleLogout = () => {
    logout();
  };

  const handleExportChats = async () => {
    try {
      const response = await fetch("/api/chats", {
        credentials: "include",
      });
      if (!response.ok) throw new Error("Failed to fetch chats");
      
      const chats = await response.json();
      const dataStr = JSON.stringify(chats, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `chatgpt-clone-export-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Export successful",
        description: "Your chat history has been downloaded",
      });
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Unable to export chat history",
        variant: "destructive",
      });
    }
  };

  const isDarkMode = theme === "dark";

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-3">
            <Link href="/">
              <Button variant="ghost" size="sm" className="p-2">
                <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </Button>
            </Link>
            <h1 className="text-lg font-semibold text-gray-800 dark:text-white">Settings</h1>
          </div>
        </div>
      </header>

      {/* Settings Content */}
      <div className="px-4 py-6 space-y-6">
        {/* Profile Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Profile</h2>
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
              {user?.profileImageUrl ? (
                <img
                  src={user.profileImageUrl}
                  alt="Profile"
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="w-8 h-8 text-white" />
              )}
            </div>
            <div>
              <h3 className="font-medium text-gray-800 dark:text-white">
                {user?.firstName && user?.lastName
                  ? `${user.firstName} ${user.lastName}`
                  : user?.email || "User"}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {user?.email || "No email available"}
              </p>
            </div>
          </div>
        </div>

        {/* Appearance */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Appearance</h2>
          <div className="flex items-center justify-between">
            <span className="text-gray-700 dark:text-gray-300">Dark Mode</span>
            <Switch
              checked={isDarkMode}
              onCheckedChange={handleToggleDarkMode}
            />
          </div>
        </div>

        {/* Chat Preferences */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Chat Preferences</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">Auto-save chats</span>
              <Switch
                checked={autoSaveChats}
                onCheckedChange={handleToggleAutoSave}
              />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-gray-300">Send on Enter</span>
              <Switch
                checked={sendOnEnter}
                onCheckedChange={handleToggleSendOnEnter}
              />
            </div>
          </div>
        </div>

        {/* API Configuration */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">AI Provider Settings</h2>

          {/* Default Provider Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Default AI Provider
            </label>
            <select
              value={defaultProvider}
              onChange={(e) => handleSaveDefaultProvider(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md text-gray-800 dark:text-white"
            >
              <option value="openai">OpenAI GPT-4o</option>
              <option value="gemini">Google Gemini 1.5 Flash</option>
              <option value="cohere">Cohere Command R+</option>
              <option value="anthropic">Anthropic Claude 3.5 Sonnet</option>
            </select>
          </div>

          <div className="space-y-6">
            {/* OpenAI API Key */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                OpenAI API Key
              </label>
              <div className="flex space-x-2">
                <div className="relative flex-1">
                  <Input
                    type="password"
                    value={openaiApiKey}
                    onChange={(e) => setOpenaiApiKey(e.target.value)}
                    placeholder="sk-..."
                    className="bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 pr-10"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    {getValidationIcon('openai')}
                  </div>
                </div>
                <Button
                  onClick={() => handleSaveApiKey('openai', openaiApiKey)}
                  disabled={updateSettingsMutation.isPending || validationStates.openai === 'validating'}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  {validationStates.openai === 'validating' ? 'Validating...' : 'Save'}
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Get your API key from{" "}
                <a
                  href="https://platform.openai.com/api-keys"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  OpenAI Platform
                </a>
              </p>
            </div>

            {/* Gemini API Key */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Google Gemini API Key
              </label>
              <div className="flex space-x-2">
                <Input
                  type="password"
                  value={geminiApiKey}
                  onChange={(e) => setGeminiApiKey(e.target.value)}
                  placeholder="AIza..."
                  className="flex-1 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                />
                <Button
                  onClick={() => handleSaveApiKey('gemini', geminiApiKey)}
                  disabled={updateSettingsMutation.isPending}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  Save
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Get your API key from{" "}
                <a
                  href="https://ai.google.dev/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  Google AI Studio
                </a>
              </p>
            </div>

            {/* Cohere API Key */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Cohere API Key
              </label>
              <div className="flex space-x-2">
                <Input
                  type="password"
                  value={cohereApiKey}
                  onChange={(e) => setCohereApiKey(e.target.value)}
                  placeholder="co..."
                  className="flex-1 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                />
                <Button
                  onClick={() => handleSaveApiKey('cohere', cohereApiKey)}
                  disabled={updateSettingsMutation.isPending}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  Save
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Get your API key from{" "}
                <a
                  href="https://dashboard.cohere.ai/api-keys"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  Cohere Dashboard
                </a>
              </p>
            </div>

            {/* Anthropic API Key */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Anthropic Claude API Key
              </label>
              <div className="flex space-x-2">
                <Input
                  type="password"
                  value={anthropicApiKey}
                  onChange={(e) => setAnthropicApiKey(e.target.value)}
                  placeholder="sk-ant-..."
                  className="flex-1 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                />
                <Button
                  onClick={() => handleSaveApiKey('anthropic', anthropicApiKey)}
                  disabled={updateSettingsMutation.isPending}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  Save
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Get your API key from{" "}
                <a
                  href="https://console.anthropic.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  Anthropic Console
                </a>
              </p>
            </div>
          </div>
        </div>

        {/* Account Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">Account</h2>
          <div className="space-y-3">
            <Button
              variant="outline"
              onClick={handleExportChats}
              className="w-full justify-start text-gray-700 dark:text-gray-300"
            >
              Export Chat History
            </Button>
          </div>
        </div>

        {/* Logout */}
        <Button
          onClick={handleLogout}
          className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200"
        >
          <LogOut className="w-5 h-5 mr-2" />
          Logout
        </Button>
      </div>
    </div>
  );
}
